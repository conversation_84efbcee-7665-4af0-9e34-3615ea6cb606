:root {
  --pointer-x: 50%;
  --pointer-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0.5;
  --pointer-from-left: 0.5;
  --card-opacity: 0;
  --rotate-x: 0deg;
  --rotate-y: 0deg;
  --background-x: 50%;
  --background-y: 50%;
  --grain: none;
  --icon: none;
  --behind-gradient: none;
  --inner-gradient: none;
  --sunpillar-1: hsl(2, 100%, 73%);
  --sunpillar-2: hsl(53, 100%, 69%);
  --sunpillar-3: hsl(93, 100%, 69%);
  --sunpillar-4: hsl(176, 100%, 76%);
  --sunpillar-5: hsl(228, 100%, 74%);
  --sunpillar-6: hsl(283, 100%, 73%);
  --sunpillar-clr-1: var(--sunpillar-1);
  --sunpillar-clr-2: var(--sunpillar-2);
  --sunpillar-clr-3: var(--sunpillar-3);
  --sunpillar-clr-4: var(--sunpillar-4);
  --sunpillar-clr-5: var(--sunpillar-5);
  --sunpillar-clr-6: var(--sunpillar-6);
}

.pc-card-wrapper {
  --card-opacity: 0.8;
  perspective: 1200px;
  width: 100%;
  height: 100%;
  aspect-ratio: 3/4;
  position: relative;
  filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5));
}

.pc-card-wrapper.active {
  --card-opacity: 1;
}

.pc-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 24px;
  transform-style: preserve-3d;
  transition: transform 0.3s ease-out;
  transform: rotateX(var(--rotate-x)) rotateY(var(--rotate-y));
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(20, 20, 40, 0.9) 0%,
    rgba(40, 40, 80, 0.8) 50%,
    rgba(20, 20, 40, 0.9) 100%);
  border: 2px solid rgba(0, 255, 255, 0.3);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.2),
    inset 0 0 30px rgba(0, 255, 255, 0.1);
}

.pc-inside {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 22px;
  background: linear-gradient(135deg,
    rgba(10, 10, 30, 0.95) 0%,
    rgba(30, 30, 60, 0.9) 50%,
    rgba(10, 10, 30, 0.95) 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 24px;
}

.pc-shine {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at var(--pointer-x) var(--pointer-y),
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  opacity: var(--pointer-from-center);
  transition: opacity 0.3s ease;
}

.pc-glare {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    transparent 40%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 60%
  );
  transform: translateX(calc(var(--pointer-x) - 50%)) 
             translateY(calc(var(--pointer-y) - 50%));
  transition: transform 0.3s ease;
}

.pc-content {
  position: relative;
  z-index: 10;
}

.pc-avatar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  flex-direction: column;
  padding: 20px;
  position: relative;
}

.pc-avatar-content::before {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.pc-avatar-content::after {
  content: '';
  position: absolute;
  bottom: 30%;
  right: 15%;
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(255, 0, 255, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-10px) scale(1.1); }
}

.avatar {
  width: 100%;
  max-width: 280px;
  height: auto;
  aspect-ratio: 3/4;
  border-radius: 16px;
  object-fit: cover;
  border: 3px solid rgba(0, 255, 255, 0.4);
  margin-bottom: 20px;
  box-shadow:
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  filter: brightness(1.1) contrast(1.1);
}



.pc-user-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  border-radius: 0 0 22px 22px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pc-user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pc-mini-avatar {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  overflow: hidden;
}

.pc-mini-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pc-user-text {
  color: white;
}

.pc-handle {
  font-weight: 600;
  font-size: 14px;
}

.pc-status {
  font-size: 12px;
  opacity: 0.8;
}

.pc-contact-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pc-contact-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.pc-details {
  display: none;
}

/* Add some particle effects */
.pc-inside::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 20%;
  width: 4px;
  height: 4px;
  background: rgba(0, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow:
    0 0 10px rgba(0, 255, 255, 0.8),
    20px 30px 0 rgba(255, 0, 255, 0.6),
    -15px 45px 0 rgba(0, 255, 255, 0.4),
    35px 60px 0 rgba(255, 255, 0, 0.5);
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}
