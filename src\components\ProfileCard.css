:root {
  --pointer-x: 50%;
  --pointer-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0.5;
  --pointer-from-left: 0.5;
  --card-opacity: 0;
  --rotate-x: 0deg;
  --rotate-y: 0deg;
  --background-x: 50%;
  --background-y: 50%;
  --grain: none;
  --icon: none;
  --behind-gradient: none;
  --inner-gradient: none;
  --sunpillar-1: hsl(2, 100%, 73%);
  --sunpillar-2: hsl(53, 100%, 69%);
  --sunpillar-3: hsl(93, 100%, 69%);
  --sunpillar-4: hsl(176, 100%, 76%);
  --sunpillar-5: hsl(228, 100%, 74%);
  --sunpillar-6: hsl(283, 100%, 73%);
  --sunpillar-clr-1: var(--sunpillar-1);
  --sunpillar-clr-2: var(--sunpillar-2);
  --sunpillar-clr-3: var(--sunpillar-3);
  --sunpillar-clr-4: var(--sunpillar-4);
  --sunpillar-clr-5: var(--sunpillar-5);
  --sunpillar-clr-6: var(--sunpillar-6);
}

.pc-card-wrapper {
  --card-opacity: 0.8;
  perspective: 1000px;
  width: 100%;
  height: 100%;
  aspect-ratio: 3/4;
  position: relative;
}

.pc-card-wrapper.active {
  --card-opacity: 1;
}

.pc-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 24px;
  transform-style: preserve-3d;
  transition: transform 0.3s ease-out;
  transform: rotateX(var(--rotate-x)) rotateY(var(--rotate-y));
  overflow: hidden;
  background: var(--behind-gradient);
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.pc-inside {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 22px;
  background: var(--inner-gradient);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 24px;
}

.pc-shine {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at var(--pointer-x) var(--pointer-y),
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  opacity: var(--pointer-from-center);
  transition: opacity 0.3s ease;
}

.pc-glare {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    transparent 40%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 60%
  );
  transform: translateX(calc(var(--pointer-x) - 50%)) 
             translateY(calc(var(--pointer-y) - 50%));
  transition: transform 0.3s ease;
}

.pc-content {
  position: relative;
  z-index: 10;
}

.pc-avatar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  flex-direction: column;
  padding: 20px;
}

.avatar {
  width: 100%;
  max-width: 280px;
  height: auto;
  aspect-ratio: 3/4;
  border-radius: 16px;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 20px;
}

.pc-user-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  border-radius: 0 0 22px 22px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pc-user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pc-mini-avatar {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  overflow: hidden;
}

.pc-mini-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pc-user-text {
  color: white;
}

.pc-handle {
  font-weight: 600;
  font-size: 14px;
}

.pc-status {
  font-size: 12px;
  opacity: 0.8;
}

.pc-contact-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pc-contact-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.pc-details {
  display: none;
}
