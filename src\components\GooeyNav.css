:root {
  --linear-ease: linear(0, 0.068, 0.19 2.7%, 0.804 8.1%, 1.037, 1.199 13.2%, 1.245, 1.27 15.8%, 1.274, 1.272 17.4%, 1.249 19.1%, 0.996 28%, 0.949, 0.928 33.3%, 0.926, 0.933 36.8%, 1.001 45.6%, 1.013, 1.019 50.8%, 1.018 54.4%, 1 63.1%, 0.995 68%, 1.001 85%, 1);
  --color-1: #ff6b6b;
  --color-2: #4ecdc4;
  --color-3: #45b7d1;
  --color-4: #96ceb4;
}

.gooey-nav-container {
  position: relative;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 8px 24px;
}

.gooey-nav-container nav {
  display: flex;
  position: relative;
  transform: translate3d(0, 0, 0.01px);
}

.gooey-nav-container nav ul {
  display: flex;
  gap: 2em;
  list-style: none;
  padding: 0 1em;
  margin: 0;
  position: relative;
  z-index: 3;
  color: white;
  text-shadow: 0 1px 1px hsl(205deg 30% 10% / 0.2);
}

.gooey-nav-container nav ul li {
  cursor: pointer;
  padding: 0.5em 1em;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.gooey-nav-container nav ul li a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
}

.gooey-nav-container nav ul li:hover {
  background: rgba(255, 255, 255, 0.1);
}

.effect {
  position: absolute;
  border-radius: 50px;
  pointer-events: none;
  z-index: 1;
}

.effect.filter {
  filter: url(#gooey);
}

.effect.text {
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.effect.text.active {
  opacity: 0;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  transform: translate(-50%, -50%) translate(var(--start-x), var(--start-y)) scale(var(--scale)) rotate(var(--rotate));
  animation: particle var(--time) var(--linear-ease) forwards;
}

.point {
  width: 100%;
  height: 100%;
  background: var(--color);
  border-radius: 50%;
}

@keyframes particle {
  to {
    transform: translate(-50%, -50%) translate(var(--end-x), var(--end-y)) scale(0) rotate(calc(var(--rotate) + 180deg));
  }
}
