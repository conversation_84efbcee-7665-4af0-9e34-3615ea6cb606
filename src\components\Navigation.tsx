
import GooeyNav from "./GooeyNav";

const Navigation = () => {
  const navItems = [
    { label: "Home", href: "#hero" },
    { label: "Projects", href: "#projects" },
    { label: "About", href: "#about" },
    { label: "Skills", href: "#skills" },
    { label: "Contact", href: "#contact" },
  ];

  return (
    <nav className="fixed top-6 right-6 z-50">
      <GooeyNav
        items={navItems}
        particleCount={15}
        particleDistances={[100, 15]}
        particleR={120}
        initialActiveIndex={0}
        animationTime={600}
        timeVariance={300}
        colors={[1, 2, 3, 1, 2, 3, 1, 4]}
      />
    </nav>
  );
};

export default Navigation;
