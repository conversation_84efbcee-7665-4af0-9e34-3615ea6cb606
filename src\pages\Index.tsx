
import { useEffect, useRef } from "react";
import Navigation from "../components/Navigation";
import Iridescence from "../components/Iridescence";
import ScrollFloat from "../components/ScrollFloat";
import ProfileCard from "../components/ProfileCard";
import VariableProximity from "../components/VariableProximity";
import BlurText from "../components/BlurText";
import { Github, Linkedin, Mail, ArrowDown, Instagram, Twitter } from "lucide-react";
import { motion, useScroll, useTransform } from "framer-motion";

const Index = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    document.documentElement.style.scrollBehavior = 'smooth';
  }, []);

  const projects = [
    {
      title: "AI Chat Assistant",
      description: "Advanced conversational AI with natural language processing and context awareness for enterprise solutions.",
      tech: ["Python", "TensorFlow", "FastAPI", "React"],
    },
    {
      title: "Computer Vision Platform", 
      description: "Real-time object detection and analysis system for industrial applications.",
      tech: ["PyTorch", "OpenCV", "Docker", "AWS"],
    },
    {
      title: "Neural Network Optimizer",
      description: "Custom optimization algorithms for deep learning model performance enhancement.",
      tech: ["JAX", "NumPy", "Kubernetes", "MLflow"],
    }
  ];

  const skills = [
    { name: "Machine Learning", level: 95 },
    { name: "Deep Learning", level: 90 },
    { name: "Python", level: 95 },
    { name: "TensorFlow/PyTorch", level: 88 },
    { name: "Computer Vision", level: 85 },
    { name: "Natural Language Processing", level: 80 }
  ];

  const SkillBar = ({ skill, index }: { skill: { name: string; level: number }, index: number }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const { scrollYProgress } = useScroll({
      target: containerRef,
      offset: ["start end", "center center"]
    });
    
    const width = useTransform(
      scrollYProgress,
      [0, 1],
      [0, skill.level]
    );

    return (
      <motion.div
        ref={containerRef}
        className="space-y-3"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        viewport={{ once: true, margin: "-100px" }}
      >
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold text-white">{skill.name}</h3>
          <span className="text-white/80 font-mono font-medium">{skill.level}%</span>
        </div>
        <div className="w-full bg-white/10 rounded-full h-3 overflow-hidden">
          <motion.div
            className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 h-3 rounded-full origin-left"
            style={{ scaleX: width }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden" ref={containerRef}>
      {/* Curved border container */}
      <div className="fixed inset-4 border-2 border-white/10 rounded-[2rem] pointer-events-none z-10" />
      
      {/* Fixed Name Badge */}
      <div className="fixed top-6 left-6 z-50">
        <div className="relative">
          <div className="relative bg-white/10 backdrop-blur-xl px-8 py-4 rounded-[50px] border border-white/20">
            <span className="text-white font-extrabold text-xl bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">Sanjay Ram A</span>
          </div>
        </div>
      </div>

      <Navigation />

      {/* Hero Section */}
      <section id="hero" ref={heroRef} className="min-h-screen relative flex items-center justify-center px-4">
        {/* Main Iridescent Background */}
        <div className="absolute inset-4 z-0">
          <div className="w-full h-full rounded-[2rem] overflow-hidden">
            <Iridescence
              color={[0.6, 0.6, 1.0]}
              mouseReact={false}
              amplitude={0.3}
              speed={1.0}
            />
          </div>
        </div>

        {/* Central Text with Variable Proximity Effect */}
        <div className="relative z-20 text-center space-y-6">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white via-white/90 to-white/80">
            Sanjay Ram
          </h1>
          <VariableProximity
            label="Building the future with AI & Design"
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-white"
            fromFontVariationSettings="'wght' 400, 'opsz' 20"
            toFontVariationSettings="'wght' 900, 'opsz' 40"
            containerRef={containerRef}
            radius={150}
            falloff="exponential"
          />
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <button 
            onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
            className="group flex items-center space-x-2 text-white/60 hover:text-white transition-all duration-300 hover:scale-105"
          >
            <span className="text-sm">Explore My Work</span>
            <ArrowDown className="w-4 h-4 group-hover:translate-y-1 transition-transform duration-300" />
          </button>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 md:py-32 px-4 md:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <ScrollFloat
            containerClassName="mb-12 md:mb-20 text-center"
            textClassName="text-3xl md:text-5xl lg:text-6xl font-bold"
          >
            Work
          </ScrollFloat>
          
          <div className="space-y-8">
            {projects.map((project, index) => (
              <motion.div 
                key={index} 
                className="border-b border-white/10 pb-8"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
                      {project.title}
                    </h3>
                    <p className="text-white/60 text-sm uppercase tracking-wider mb-4">
                      {project.tech.join(" • ")}
                    </p>
                    <p className="text-white/80 max-w-2xl">
                      {project.description}
                    </p>
                  </div>
                  <div className="text-white/40 text-lg font-mono">
                    0{index + 1}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 md:py-32 px-4 md:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <ScrollFloat
            containerClassName="mb-12 md:mb-20 text-center"
            textClassName="text-3xl md:text-5xl lg:text-6xl font-bold"
          >
            About Me
          </ScrollFloat>
          
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <div className="flex justify-center">
              <div className="w-80 h-[400px]">
                <ProfileCard
                  avatarUrl="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                  handle="sanjayram"
                  status="AI developer"
                  contactText="Let's Connect"
                  enableTilt={true}
                  showUserInfo={true}
                  onContactClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                />
              </div>
            </div>
            
            <div className="space-y-6">
              <p className="text-base md:text-lg text-white/80 leading-relaxed">
                I'm a web developer & designer based in Cluj-Napoca! I specialize in Frontend Engineering, focusing on building high quality web experiences through clean code and thoughtful design.
              </p>
              <div className="flex space-x-4 pt-6">
                <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                  <Github className="w-5 h-5" />
                </a>
                <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                  <Linkedin className="w-5 h-5" />
                </a>
                <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                  <Mail className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-20 md:py-32 px-4 md:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <ScrollFloat
            containerClassName="mb-12 md:mb-20 text-center"
            textClassName="text-3xl md:text-5xl lg:text-6xl font-bold"
          >
            Skills & Expertise
          </ScrollFloat>
          
          <div className="grid md:grid-cols-2 gap-8">
            {skills.map((skill, index) => (
              <SkillBar key={index} skill={skill} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="relative min-h-screen">
        <div className="absolute inset-x-4 bottom-4 h-[80vh]">
          <div className="w-full h-full rounded-[2rem] overflow-hidden">
            <Iridescence
              color={[0.6, 0.6, 1.0]}
              mouseReact={false}
              amplitude={0.3}
              speed={1.0}
            />
          </div>
        </div>
        
        <div className="relative z-20 h-[80vh] flex flex-col items-center justify-center text-center px-4 mb-4">
          <BlurText
            text="Wanna create something together?"
            className="text-3xl md:text-5xl font-bold text-white mt-[20vh]"
            delay={300}
            animateBy="words"
            direction="top"
          />
          
          <div className="flex flex-col items-center gap-6 mt-8">
            <button className="flex items-center space-x-3 bg-white/20 backdrop-blur-md px-6 py-3 rounded-full hover:scale-105 transition-transform duration-300 border border-white/30">
              <span className="font-medium text-white">Let's talk</span>
            </button>
            
            <div className="flex items-center space-x-4">
              <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <Github className="w-5 h-5 text-white" />
              </a>
              <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <Linkedin className="w-5 h-5 text-white" />
              </a>
              <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <Mail className="w-5 h-5 text-white" />
              </a>
              <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <Instagram className="w-5 h-5 text-white" />
              </a>
              <a href="#" className="p-3 bg-white/10 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <Twitter className="w-5 h-5 text-white" />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
